import NextAuth from 'next-auth';
import { authOptions } from './auth-options';

// Export authOptions for use in API routes
export { authOptions };

export const { handlers, auth, signIn, signOut } = NextAuth({
  ...authOptions,
  session: { strategy: 'jwt' as const },
});

export async function getCurrentUser() {
  const session = await auth();
  return session?.user || null;
}

export async function requireAuth() {
  const user = await getCurrentUser();
  if (!user) {
    throw new Error('Not authenticated');
  }
  return user;
}

export async function isAuthenticated() {
  try {
    await requireAuth();
    return true;
  } catch {
    return false;
  }
}
