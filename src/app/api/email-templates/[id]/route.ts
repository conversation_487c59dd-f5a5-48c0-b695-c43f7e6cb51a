import { NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import EmailTemplate from '@/models/EmailTemplate';
import { auth } from '@/lib/auth';

// GET /api/email-templates/[id] - Get a single email template
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const template = await EmailTemplate.findById(params.id);
    if (!template) {
      return NextResponse.json(
        { error: 'Email template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(template);
  } catch (error) {
    console.error('Error fetching email template:', error);
    return NextResponse.json(
      { error: 'Failed to fetch email template' },
      { status: 500 }
    );
  }
}

// PUT /api/email-templates/[id] - Update an email template
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();
    
    // Find the existing template
    const template = await EmailTemplate.findById(params.id);
    if (!template) {
      return NextResponse.json(
        { error: 'Email template not found' },
        { status: 404 }
      );
    }

    // Extract variables from updated template
    const variables = [...new Set([
      ...(data.html?.match(/{{(.*?)}}/g) || []).map((v: string) => v.replace(/[{}]/g, '')),
      ...(data.text?.match(/{{(.*?)}}/g) || []).map((v: string) => v.replace(/[{}]/g, ''))
    ])];

    // Update template fields
    template.name = data.name || template.name;
    template.slug = data.slug || template.slug;
    template.subject = data.subject || template.subject;
    template.html = data.html !== undefined ? data.html : template.html;
    template.text = data.text !== undefined ? data.text : template.text;
    template.variables = variables;
    template.isActive = data.isActive !== undefined ? data.isActive : template.isActive;

    await template.save();
    return NextResponse.json(template);
  } catch (error) {
    console.error('Error updating email template:', error);
    return NextResponse.json(
      { error: 'Failed to update email template' },
      { status: 500 }
    );
  }
}

// DELETE /api/email-templates/[id] - Delete an email template
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    await dbConnect();

    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const template = await EmailTemplate.findByIdAndDelete(params.id);
    if (!template) {
      return NextResponse.json(
        { error: 'Email template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Email template deleted successfully' });
  } catch (error) {
    console.error('Error deleting email template:', error);
    return NextResponse.json(
      { error: 'Failed to delete email template' },
      { status: 500 }
    );
  }
}
